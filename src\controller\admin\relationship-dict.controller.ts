import {
  Controller,
  Post,
  Put,
  Del,
  Get,
  Body,
  Param,
  Query,
  Inject,
} from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { JwtMiddleware } from '../../middleware/jwt.middleware';
import { AuthMiddleware } from '../../middleware/auth.middleware';
import { RelationshipDictService } from '../../service/relationship-dict.service';
import {
  CreateRelationshipDictDTO,
  UpdateRelationshipDictDTO,
  RelationshipDictQueryDTO,
  BatchUpdateStatusDTO,
} from '../../dto/dictionary.dto';

/**
 * 关系字典管理控制器
 */
@Controller('/admin/relationship-dict', {
  middleware: [JwtMiddleware, AuthMiddleware],
})
export class AdminRelationshipDictController {
  @Inject()
  relationshipDictService: RelationshipDictService;

  /**
   * 创建关系字典
   */
  @Post('/')
  @Validate()
  async create(@Body() createDto: CreateRelationshipDictDTO) {
    console.log('🔗 创建关系字典:', createDto);

    try {
      const data = await this.relationshipDictService.createRelationshipDict(
        createDto
      );
      console.log('🔗 关系字典创建成功:', data.id);
      return data;
    } catch (error) {
      console.error('🔗 关系字典创建失败:', error);
      throw error;
    }
  }

  /**
   * 更新关系字典
   */
  @Put('/:id')
  @Validate()
  async update(
    @Param('id') id: number,
    @Body() updateDto: UpdateRelationshipDictDTO
  ) {
    console.log('🔗 更新关系字典:', { id, updateDto });

    try {
      const data = await this.relationshipDictService.updateRelationshipDict(
        id,
        updateDto
      );
      console.log('🔗 关系字典更新成功:', data.id);
      return data;
    } catch (error) {
      console.error('🔗 关系字典更新失败:', error);
      throw error;
    }
  }

  /**
   * 删除关系字典
   */
  @Del('/:id')
  async delete(@Param('id') id: number) {
    console.log('🔗 删除关系字典:', id);

    try {
      await this.relationshipDictService.deleteRelationshipDict(id);
      console.log('🔗 关系字典删除成功:', id);
      return { message: '删除成功' };
    } catch (error) {
      console.error('🔗 关系字典删除失败:', error);
      throw error;
    }
  }

  /**
   * 获取关系字典详情
   */
  @Get('/:id')
  async getById(@Param('id') id: number) {
    console.log('🔗 获取关系字典详情:', id);

    try {
      const data = await this.relationshipDictService.getRelationshipDictById(
        id
      );
      if (!data) {
        throw new Error('关系字典不存在');
      }
      console.log('🔗 关系字典详情获取成功:', data.id);
      return data;
    } catch (error) {
      console.error('🔗 关系字典详情获取失败:', error);
      throw error;
    }
  }

  /**
   * 获取关系字典列表（分页）
   */
  @Get('/')
  @Validate()
  async getList(@Query() queryDto: RelationshipDictQueryDTO) {
    console.log('🔗 关系字典列表查询开始:', queryDto);

    try {
      const data = await this.relationshipDictService.getRelationshipDictPage(
        queryDto
      );
      console.log('🔗 关系字典列表查询成功:', {
        total: data.total,
        page: data.page,
        pageSize: data.pageSize,
      });
      return data;
    } catch (error) {
      console.error('🔗 关系字典列表查询失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有关系字典（缓存）
   */
  @Get('/all')
  async getAll() {
    console.log('🔗 获取所有关系字典');

    try {
      const data = await this.relationshipDictService.getRelationshipDictList();
      console.log('🔗 所有关系字典获取成功:', data.length);
      return data;
    } catch (error) {
      console.error('🔗 所有关系字典获取失败:', error);
      throw error;
    }
  }

  /**
   * 获取关系字典树形结构
   */
  @Get('/tree')
  async getTree() {
    console.log('🔗 获取关系字典树形结构');

    try {
      const data = await this.relationshipDictService.getRelationshipDictTree();
      console.log('🔗 关系字典树形结构获取成功:', data.length);
      return data;
    } catch (error) {
      console.error('🔗 关系字典树形结构获取失败:', error);
      throw error;
    }
  }

  /**
   * 批量更新状态
   */
  @Put('/batch-status')
  @Validate()
  async batchUpdateStatus(@Body() batchDto: BatchUpdateStatusDTO) {
    console.log('🔗 批量更新关系字典状态:', batchDto);

    try {
      await this.relationshipDictService.batchUpdateStatus(
        batchDto.ids,
        batchDto.status
      );
      console.log('🔗 批量更新关系字典状态成功');
      return { message: '批量更新成功' };
    } catch (error) {
      console.error('🔗 批量更新关系字典状态失败:', error);
      throw error;
    }
  }

  /**
   * 启用/禁用关系字典
   */
  @Post('/:id/toggle-status')
  async toggleStatus(@Param('id') id: number) {
    console.log('🔗 切换关系字典状态:', id);

    try {
      const result = await this.relationshipDictService.toggleStatus(id);
      console.log('🔗 关系字典状态切换成功:', result);
      return result;
    } catch (error) {
      console.error('🔗 关系字典状态切换失败:', error);
      throw error;
    }
  }
}
