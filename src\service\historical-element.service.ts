import { Provide, Inject } from '@midwayjs/core';
import { ModelCtor } from 'sequelize-typescript';
import { HistoricalElement } from '../entity/historical-element.entity';
import { PageQueryDTO, PageResponseDTO } from '../dto/common.dto';
import {
  CreateHistoricalElementDTO,
  UpdateHistoricalElementDTO,
} from '../dto/entity.dto';
import { CacheService } from './cache.service';
import { BaseService } from '../common/BaseService';
import { RegionDict, TypeDict } from '../entity';

@Provide()
export class HistoricalElementService extends BaseService<HistoricalElement> {
  @Inject()
  cacheService: CacheService;

  constructor() {
    super('历史要素');
  }

  protected getModel(): ModelCtor<HistoricalElement> {
    return HistoricalElement;
  }

  /**
   * 创建历史要素（业务逻辑封装）
   */
  async createHistoricalElement(
    createDto: CreateHistoricalElementDTO
  ): Promise<HistoricalElement> {
    await this.validateHistoricalElementData(createDto);
    return await this.create(createDto as any);
  }

  /**
   * 更新历史要素（业务逻辑封装）
   */
  async updateHistoricalElement(
    id: number,
    updateDto: UpdateHistoricalElementDTO
  ): Promise<HistoricalElement> {
    await this.validateHistoricalElementData(updateDto);
    await this.update({ id }, updateDto as any);
    return (await this.findById(id)) as HistoricalElement;
  }

  /**
   * 删除历史要素（业务逻辑封装）
   * @param id 历史要素ID
   * @param deletePhotos 是否同时删除关联的照片，默认为true
   */
  async deleteHistoricalElement(
    id: number,
    deletePhotos = true
  ): Promise<void> {
    // 检查历史要素是否存在
    const element = await this.findById(id);
    if (!element) {
      throw new Error('历史要素不存在');
    }

    try {
      // 根据参数决定是否删除关联的照片
      if (deletePhotos) {
        // 如果有照片服务，可以调用照片服务来删除关联照片
        // await this.photoService.deleteByHistoricalElementId(id);
        //
        // 或者通过数据库级联删除（需要在数据库层面配置外键约束）
        // 这里暂时注释，等照片服务实现后再启用
      }

      // 删除历史要素本身
      await this.delete({ id });

      // 注意：这里不需要刷新字典缓存，因为删除历史要素不会影响字典数据
      // 字典缓存只在字典数据变更时才需要刷新
    } catch (error) {
      throw new Error(`删除历史要素失败: ${error.message}`);
    }
  }

  /**
   * 分页查询历史要素列表
   */
  async findList(
    query: PageQueryDTO & { typeId?: number }
  ): Promise<PageResponseDTO<HistoricalElement>> {
    const { page, pageSize, keyword, regionId, typeId } = query;
    const offset = (page - 1) * pageSize;

    const whereConditions: any = {};

    if (keyword) {
      whereConditions.name = { [Symbol.for('like')]: `%${keyword}%` };
    }

    if (regionId) {
      whereConditions.regionDictId = regionId;
    }

    if (typeId) {
      whereConditions.typeDictId = typeId;
    }

    const result = await this.findAll({
      query: whereConditions,
      offset,
      limit: pageSize,
      order: [['createdAt', 'DESC']],
    });

    return new PageResponseDTO(result.list, result.total || 0, page, pageSize);
  }

  /**
   * 根据类型获取历史要素列表
   */
  async findByType(typeId: number): Promise<HistoricalElement[]> {
    const result = await this.findAll({
      query: { typeDictId: typeId } as any,
      order: [['name', 'ASC']],
    });
    return result.list;
  }

  /**
   * 根据区域获取历史要素列表
   */
  async findByRegion(regionId: number): Promise<HistoricalElement[]> {
    const result = await this.findAll({
      query: { regionDictId: regionId } as any,
      order: [['name', 'ASC']],
    });
    return result.list;
  }

  /**
   * 根据建造时间范围查询
   */
  async findByConstructionTime(
    startTime?: Date,
    endTime?: Date
  ): Promise<HistoricalElement[]> {
    const whereConditions: any = {};

    if (startTime) {
      whereConditions.constructionTime = { [Symbol.for('gte')]: startTime };
    }

    if (endTime) {
      if (whereConditions.constructionTime) {
        whereConditions.constructionTime[Symbol.for('lte')] = endTime;
      } else {
        whereConditions.constructionTime = { [Symbol.for('lte')]: endTime };
      }
    }

    const result = await this.findAll({
      query: whereConditions,
      order: [['constructionTime', 'ASC']],
    });
    return result.list;
  }

  /**
   * 获取历史要素统计数据
   */
  async getStatistics(
    regionId?: number,
    typeId?: number
  ): Promise<{
    total: number;
    byType: Array<{ typeId: number; typeName: string; count: number }>;
    byRegion: Array<{ regionId: number; regionName: string; count: number }>;
    byPeriod: Array<{ period: string; count: number }>;
  }> {
    const whereConditions: any = {};

    if (regionId) {
      whereConditions.regionDictId = regionId;
    }

    if (typeId) {
      whereConditions.typeDictId = typeId;
    }

    // 获取所有历史要素数据，包含关联的类型和区域信息
    const result = await this.findAll({
      query: whereConditions,
      include: [
        { model: TypeDict, as: 'typeDict' },
        { model: RegionDict, as: 'regionDict' },
      ],
    });
    const total = result.total || 0;
    const elements = result.list;

    // 按类型统计
    const typeMap = new Map<
      number,
      { typeId: number; typeName: string; count: number }
    >();
    // 按区域统计
    const regionMap = new Map<
      number,
      { regionId: number; regionName: string; count: number }
    >();
    // 按时期统计
    const periodMap = new Map<string, number>();

    elements.forEach(element => {
      // 统计类型
      if (element.typeDictId && element.typeDict) {
        const typeId = element.typeDictId;
        if (typeMap.has(typeId)) {
          typeMap.get(typeId)!.count++;
        } else {
          typeMap.set(typeId, {
            typeId,
            typeName: element.typeDict.typeName,
            count: 1,
          });
        }
      }

      // 统计区域
      if (element.regionDictId && element.regionDict) {
        const regionId = element.regionDictId;
        if (regionMap.has(regionId)) {
          regionMap.get(regionId)!.count++;
        } else {
          regionMap.set(regionId, {
            regionId,
            regionName: element.regionDict.regionName,
            count: 1,
          });
        }
      }

      // 统计时期（按朝代）
      if (element.constructionTime) {
        const year = new Date(element.constructionTime).getFullYear();
        const period = this.getPeriodByYear(year);
        periodMap.set(period, (periodMap.get(period) || 0) + 1);
      }
    });

    const byType = Array.from(typeMap.values()).sort(
      (a, b) => b.count - a.count
    );
    const byRegion = Array.from(regionMap.values()).sort(
      (a, b) => b.count - a.count
    );
    const byPeriod = Array.from(periodMap.entries())
      .map(([period, count]) => ({
        period,
        count,
      }))
      .sort((a, b) => b.count - a.count);

    return {
      total,
      byType,
      byRegion,
      byPeriod,
    };
  }

  /**
   * 根据年份获取历史时期
   */
  private getPeriodByYear(year: number): string {
    if (year < 221) return '先秦';
    if (year < 206) return '秦朝';
    if (year < 220) return '汉朝';
    if (year < 280) return '三国';
    if (year < 420) return '晋朝';
    if (year < 589) return '南北朝';
    if (year < 618) return '隋朝';
    if (year < 907) return '唐朝';
    if (year < 960) return '五代十国';
    if (year < 1127) return '北宋';
    if (year < 1279) return '南宋';
    if (year < 1368) return '元朝';
    if (year < 1644) return '明朝';
    if (year < 1912) return '清朝';
    if (year < 1949) return '民国';
    return '现代';
  }

  /**
   * 获取时间轴数据
   */
  async getTimelineData(regionId?: number): Promise<
    Array<{
      year: number;
      count: number;
      elements: Array<{ id: number; name: string; type: string }>;
    }>
  > {
    const whereConditions: any = {
      constructionTime: { [Symbol.for('ne')]: null },
    };

    if (regionId) {
      whereConditions.regionDictId = regionId;
    }

    const result = await this.findAll({ query: whereConditions });
    const elements = result.list;

    // 按年份分组
    const timelineMap = new Map<
      number,
      Array<{ id: number; name: string; type: string }>
    >();

    elements.forEach(element => {
      if (element.constructionTime) {
        const year = new Date(element.constructionTime).getFullYear();
        if (!timelineMap.has(year)) {
          timelineMap.set(year, []);
        }
        timelineMap.get(year)!.push({
          id: element.id,
          name: element.name,
          type: 'historical_element',
        });
      }
    });

    return Array.from(timelineMap.entries()).map(([year, elements]) => ({
      year,
      count: elements.length,
      elements,
    }));
  }

  /**
   * 批量导入历史要素数据
   */
  async batchImportElements(
    elements: CreateHistoricalElementDTO[]
  ): Promise<void> {
    // 批量验证数据
    for (const element of elements) {
      await this.validateHistoricalElementData(element);
    }

    await this.batchCreate(elements as any);
  }

  /**
   * 验证历史要素数据
   */
  private async validateHistoricalElementData(
    data: CreateHistoricalElementDTO | UpdateHistoricalElementDTO
  ): Promise<void> {
    if (!data.name || data.name.trim().length === 0) {
      throw new Error('历史要素名称不能为空');
    }

    if (!data.constructionLongitude || !data.constructionLatitude) {
      throw new Error('建筑经纬度不能为空');
    }

    // 验证经纬度范围
    if (data.constructionLongitude < -180 || data.constructionLongitude > 180) {
      throw new Error('建筑经度范围应在-180到180之间');
    }

    if (data.constructionLatitude < -90 || data.constructionLatitude > 90) {
      throw new Error('建筑纬度范围应在-90到90之间');
    }

    // 验证建造时间
    if (data.constructionTime) {
      const constructionDate = new Date(data.constructionTime);
      const now = new Date();

      if (constructionDate > now) {
        throw new Error('建造时间不能晚于当前时间');
      }
    }
  }
}
