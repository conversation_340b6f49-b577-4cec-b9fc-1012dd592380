# 字典管理 API 文档

## 概述

字典管理模块按照三个字典类型进行了拆分，每个字典类型都有独立的控制器和完整的CRUD操作。

## 模块结构

- **区域字典管理**: `/admin/region-dict` - 管理地理区域信息
- **类型字典管理**: `/admin/type-dict` - 管理历史要素类型信息
- **关系字典管理**: `/admin/relationship-dict` - 管理实体间关系信息

## 认证信息

- **认证方式**: JWT Token + 权限验证
- **请求头**:
  ```
  Authorization: Bearer <token>
  Content-Type: application/json
  ```

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {},
  "message": "操作成功"
}
```

### 错误响应
```json
{
  "success": false,
  "code": "ERROR_CODE",
  "message": "错误信息",
  "data": null
}
```

---

## 1. 区域字典管理 API

### 基础路径: `/admin/region-dict`

#### 1.1 创建区域字典

**接口信息**
- **URL**: `POST /admin/region-dict/`
- **认证**: 需要JWT Token + 管理员权限

**请求参数**
```json
{
  "regionCode": "BJ",
  "regionName": "北京市",
  "parentId": null,
  "status": 1,
  "sort": 1,
  "regionDesc": "首都"
}
```

**参数说明**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionCode | string | 是 | 区域编码，最大50字符，唯一 |
| regionName | string | 是 | 区域名称，最大255字符 |
| parentId | number | 否 | 父级区域ID |
| status | number | 否 | 状态，0禁用 1启用，默认1 |
| sort | number | 否 | 排序号 |
| regionDesc | string | 否 | 区域描述 |

**响应示例**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "regionCode": "BJ",
    "regionName": "北京市",
    "parentId": null,
    "status": 1,
    "sort": 1,
    "regionDesc": "首都",
    "createdAt": "2024-01-15T10:00:00.000Z",
    "updatedAt": "2024-01-15T10:00:00.000Z"
  }
}
```

#### 1.2 更新区域字典

**接口信息**
- **URL**: `PUT /admin/region-dict/:id`
- **认证**: 需要JWT Token + 管理员权限

**请求参数**: 同创建参数（可选）

#### 1.3 删除区域字典

**接口信息**
- **URL**: `DELETE /admin/region-dict/:id`
- **认证**: 需要JWT Token + 管理员权限

**功能说明**
- 支持级联删除：删除父级区域时会自动删除所有子级区域
- 删除操作不可逆，请谨慎操作

**响应示例**
```json
{
  "success": true,
  "data": {
    "message": "删除成功"
  }
}
```

#### 1.4 获取区域字典详情

**接口信息**
- **URL**: `GET /admin/region-dict/:id`
- **认证**: 需要JWT Token + 管理员权限

**响应示例**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "regionCode": "BJ",
    "regionName": "北京市",
    "parentId": null,
    "status": 1,
    "sort": 1,
    "regionDesc": "首都",
    "parent": null,
    "children": [
      {
        "id": 2,
        "regionName": "海淀区",
        "regionCode": "HD"
      }
    ]
  }
}
```

#### 1.5 获取区域字典列表（分页）

**接口信息**
- **URL**: `GET /admin/region-dict/`
- **认证**: 需要JWT Token + 管理员权限

**查询参数**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | number | 否 | 页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10 |
| keyword | string | 否 | 搜索关键词（编码或名称） |
| status | number | 否 | 状态筛选 |
| parentId | number | 否 | 父级ID筛选 |

**响应示例**
```json
{
  "success": true,
  "data": {
    "list": [
      {
        "id": 1,
        "regionCode": "BJ",
        "regionName": "北京市",
        "parentId": null,
        "status": 1,
        "sort": 1,
        "regionDesc": "首都",
        "parent": null
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  }
}
```

#### 1.6 获取区域字典树形结构

**接口信息**
- **URL**: `GET /admin/region-dict/tree`
- **认证**: 需要JWT Token + 管理员权限

**响应示例**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "regionCode": "BJ",
      "regionName": "北京市",
      "parentId": null,
      "status": 1,
      "sort": 1,
      "regionDesc": "首都",
      "children": [
        {
          "id": 2,
          "regionCode": "HD",
          "regionName": "海淀区",
          "parentId": 1,
          "status": 1,
          "sort": 1,
          "children": []
        }
      ]
    }
  ]
}
```

#### 1.7 获取所有区域字典（缓存）

**接口信息**
- **URL**: `GET /admin/region-dict/all`
- **认证**: 需要JWT Token + 管理员权限

#### 1.8 批量更新状态

**接口信息**
- **URL**: `PUT /admin/region-dict/batch-status`
- **认证**: 需要JWT Token + 管理员权限

**请求参数**
```json
{
  "ids": [1, 2, 3],
  "status": 1
}
```

#### 1.9 启用/禁用区域字典

**接口信息**
- **URL**: `POST /admin/region-dict/:id/toggle-status`
- **认证**: 需要JWT Token + 管理员权限

**响应示例**
```json
{
  "success": true,
  "data": {
    "status": 1,
    "message": "区域字典已启用"
  }
}
```

---

## 2. 类型字典管理 API

### 基础路径: `/admin/type-dict`

#### 2.1 创建类型字典

**接口信息**
- **URL**: `POST /admin/type-dict/`
- **认证**: 需要JWT Token + 管理员权限

**请求参数**
```json
{
  "typeCode": "BUILDING",
  "typeName": "建筑类型",
  "parentId": null,
  "status": 1,
  "sort": 1,
  "typeDesc": "历史建筑类型"
}
```

#### 2.2 更新类型字典
- **URL**: `PUT /admin/type-dict/:id`

#### 2.3 删除类型字典
- **URL**: `DELETE /admin/type-dict/:id`
- **功能说明**: 支持级联删除，删除父级类型时会自动删除所有子级类型

#### 2.4 获取类型字典详情
- **URL**: `GET /admin/type-dict/:id`

#### 2.5 获取类型字典列表（分页）
- **URL**: `GET /admin/type-dict/`
- **查询参数**: 同区域字典

#### 2.6 获取类型字典树形结构
- **URL**: `GET /admin/type-dict/tree`

#### 2.7 获取所有类型字典（缓存）
- **URL**: `GET /admin/type-dict/all`

#### 2.8 批量更新状态
- **URL**: `PUT /admin/type-dict/batch-status`

#### 2.9 启用/禁用类型字典
- **URL**: `POST /admin/type-dict/:id/toggle-status`

---

## 3. 关系字典管理 API

### 基础路径: `/admin/relationship-dict`

#### 3.1 创建关系字典

**接口信息**
- **URL**: `POST /admin/relationship-dict/`
- **认证**: 需要JWT Token + 管理员权限

**请求参数**
```json
{
  "relationCode": "BELONG_TO",
  "relationName": "属于",
  "parentId": null,
  "status": 1,
  "sort": 1,
  "relationDesc": "归属关系"
}
```

#### 3.2 更新关系字典
- **URL**: `PUT /admin/relationship-dict/:id`

#### 3.3 删除关系字典
- **URL**: `DELETE /admin/relationship-dict/:id`
- **功能说明**: 支持级联删除，删除父级关系时会自动删除所有子级关系

#### 3.4 获取关系字典详情
- **URL**: `GET /admin/relationship-dict/:id`

#### 3.5 获取关系字典列表（分页）
- **URL**: `GET /admin/relationship-dict/`
- **查询参数**: 同区域字典

#### 3.6 获取关系字典树形结构
- **URL**: `GET /admin/relationship-dict/tree`

#### 3.7 获取所有关系字典（缓存）
- **URL**: `GET /admin/relationship-dict/all`

#### 3.8 批量更新状态
- **URL**: `PUT /admin/relationship-dict/batch-status`

#### 3.9 启用/禁用关系字典
- **URL**: `POST /admin/relationship-dict/:id/toggle-status`

---

## 数据验证规则

### 通用规则
- 编码字段：必填，最大长度50，唯一
- 名称字段：必填，最大长度255
- 状态字段：0或1（0禁用，1启用）
- 排序字段：整数
- 描述字段：可选，文本类型

### 特殊规则
- 父级ID必须是有效的同类型字典ID
- 删除时自动级联删除所有子级字典（不可逆）
- 编码更新时检查唯一性

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| DICT_CODE_EXISTS | 字典编码已存在 |
| DICT_NOT_FOUND | 字典不存在 |
| PARENT_NOT_FOUND | 父级字典不存在 |
| VALIDATION_ERROR | 参数验证失败 |

## 功能特性

1. **模块化设计**: 按字典类型拆分，便于维护和扩展
2. **完整的CRUD操作**: 支持创建、读取、更新、删除
3. **树形结构管理**: 支持无限层级的父子关系
4. **分页查询**: 支持分页、搜索、筛选
5. **批量操作**: 支持批量更新状态
6. **缓存机制**: 自动缓存管理，提高查询性能
7. **数据验证**: 完整的参数验证和业务规则检查
8. **权限控制**: 基于JWT和权限中间件的访问控制

## 使用建议

1. 优先使用缓存接口（`/all`）获取字典数据
2. 大量数据时使用分页接口
3. 树形结构展示使用 `/tree` 接口
4. 批量操作时使用批量接口提高效率
