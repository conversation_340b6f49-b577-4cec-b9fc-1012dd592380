import {
  Controller,
  Post,
  Put,
  Del,
  Get,
  Body,
  Param,
  Query,
  Inject,
} from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { JwtMiddleware } from '../../middleware/jwt.middleware';
import { AuthMiddleware } from '../../middleware/auth.middleware';
import { HistoricalElementService } from '../../service/historical-element.service';
import { PageQueryDTO } from '../../dto/common.dto';
import {
  CreateHistoricalElementDTO,
  UpdateHistoricalElementDTO,
} from '../../dto/entity.dto';

/**
 * 历史要素管理控制器
 */
@Controller('/admin/historical-element', {
  middleware: [JwtMiddleware, AuthMiddleware],
})
export class AdminHistoricalElementController {
  @Inject()
  historicalElementService: HistoricalElementService;

  /**
   * 创建历史要素
   */
  @Post('/')
  @Validate()
  async create(@Body() createDto: CreateHistoricalElementDTO) {
    const data = await this.historicalElementService.createHistoricalElement(
      createDto
    );
    return data;
  }

  /**
   * 更新历史要素
   */
  @Put('/:id')
  @Validate()
  async update(
    @Param('id') id: number,
    @Body() updateDto: UpdateHistoricalElementDTO
  ) {
    const data = await this.historicalElementService.updateHistoricalElement(
      id,
      updateDto
    );
    return data;
  }

  /**
   * 删除历史要素
   */
  @Del('/:id')
  async delete(@Param('id') id: number) {
    await this.historicalElementService.deleteHistoricalElement(id);
    return { message: '删除成功' };
  }

  /**
   * 获取历史要素列表
   */
  @Get('/')
  @Validate()
  async getList(@Query() query: PageQueryDTO & { typeId?: number }) {
    const data = await this.historicalElementService.findList(query);
    return data;
  }

  /**
   * 获取历史要素详情
   */
  @Get('/:id')
  async getDetail(@Param('id') id: number) {
    const data = await this.historicalElementService.findById(id);
    if (!data) {
      throw new Error('历史要素不存在');
    }
    return data;
  }

  /**
   * 批量导入历史要素
   */
  @Post('/batch-import')
  @Validate()
  async batchImport(@Body() data: { elements: CreateHistoricalElementDTO[] }) {
    await this.historicalElementService.batchImportElements(data.elements);
    return { message: '批量导入成功' };
  }

  /**
   * 获取历史要素统计
   */
  @Get('/statistics/overview')
  async getStatistics(
    @Query('regionId') regionId?: number,
    @Query('typeId') typeId?: number
  ) {
    const data = await this.historicalElementService.getStatistics(
      regionId,
      typeId
    );
    return data;
  }

  /**
   * 根据类型获取历史要素
   */
  @Get('/by-type/:typeId')
  async getByType(@Param('typeId') typeId: number) {
    const data = await this.historicalElementService.findByType(typeId);
    return data;
  }

  /**
   * 根据区域获取历史要素
   */
  @Get('/by-region/:regionId')
  async getByRegion(@Param('regionId') regionId: number) {
    const data = await this.historicalElementService.findByRegion(regionId);
    return data;
  }

  /**
   * 根据建造时间范围查询
   */
  @Get('/by-construction-time')
  async getByConstructionTime(
    @Query('startTime') startTime?: string,
    @Query('endTime') endTime?: string
  ) {
    const start = startTime ? new Date(startTime) : undefined;
    const end = endTime ? new Date(endTime) : undefined;
    const data = await this.historicalElementService.findByConstructionTime(
      start,
      end
    );
    return data;
  }

  /**
   * 获取时间轴数据
   */
  @Get('/timeline')
  async getTimelineData(@Query('regionId') regionId?: number) {
    const data = await this.historicalElementService.getTimelineData(regionId);
    return data;
  }
}
