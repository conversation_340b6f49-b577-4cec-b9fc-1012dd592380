import {
  Controller,
  Post,
  Put,
  Del,
  Get,
  Body,
  Param,
  Query,
  Inject,
  Files,
  Fields,
} from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { UploadFileInfo, UploadMiddleware } from '@midwayjs/busboy';
import { JwtMiddleware } from '../../middleware/jwt.middleware';
import { AuthMiddleware } from '../../middleware/auth.middleware';
import { PhotoService } from '../../service/photo.service';
import { UploadService } from '../../service/upload.service';
import { PageQueryDTO } from '../../dto/common.dto';
import { CreatePhotoDTO, UpdatePhotoDTO } from '../../dto/entity.dto';
import { promises as fs } from 'fs';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';

/**
 * 资源管理控制器
 */
@Controller('/admin/photo', { middleware: [JwtMiddleware, AuthMiddleware] })
export class AdminPhotoController {
  @Inject()
  photoService: PhotoService;

  @Inject()
  uploadService: UploadService;

  /**
   * 创建照片记录
   */
  @Post('/')
  @Validate()
  async create(@Body() createDto: CreatePhotoDTO) {
    const data = await this.photoService.createPhoto(createDto);
    return data;
  }

  /**
   * 更新照片记录
   */
  @Put('/:id')
  @Validate()
  async update(@Param('id') id: number, @Body() updateDto: UpdatePhotoDTO) {
    const data = await this.photoService.updatePhoto(id, updateDto);
    return data;
  }

  /**
   * 删除照片记录
   */
  @Del('/:id')
  async delete(@Param('id') id: number) {
    await this.photoService.deletePhoto(id);
    return { message: '删除成功' };
  }

  /**
   * 获取照片列表
   */
  @Get('/')
  @Validate()
  async getList(@Query() query: PageQueryDTO) {
    const data = await this.photoService.findPhotoList(query);
    return data;
  }

  /**
   * 获取照片详情
   */
  @Get('/:id')
  async getDetail(@Param('id') id: number) {
    const data = await this.photoService.findById(id);
    if (!data) {
      throw new Error('照片记录不存在');
    }
    return data;
  }

  /**
   * 根据实体类型和ID获取照片列表
   */
  @Get('/entity/:entityType/:entityId')
  async getPhotosByEntity(
    @Param('entityType')
    entityType: 'mountain' | 'waterSystem' | 'historicalElement',
    @Param('entityId') entityId: number
  ) {
    const data = await this.photoService.findPhotosByEntity(
      entityType,
      entityId
    );
    return data;
  }

  /**
   * 获取照片统计信息
   */
  @Get('/statistics/overview')
  async getStatistics() {
    const data = await this.photoService.getPhotoStatistics();
    return data;
  }

  /**
   * 上传照片文件并创建记录
   */
  @Post('/upload', { middleware: [UploadMiddleware] })
  @Validate()
  async uploadPhoto(
    @Files() files: UploadFileInfo[],
    @Fields() fields: Record<string, string>
  ) {
    if (!files || files.length === 0) {
      throw new Error('请选择要上传的文件');
    }

    const file = files[0];

    // 从fields中获取参数
    const photoName = fields.photoName;
    const entityType = fields.entityType as
      | 'mountain'
      | 'waterSystem'
      | 'historicalElement'
      | undefined;
    const entityId = fields.entityId ? parseInt(fields.entityId) : undefined;

    // 验证文件类型
    if (!this.uploadService.validateFileType(file.filename)) {
      throw new Error('不支持的文件类型，仅支持图片文件');
    }

    // 验证文件大小
    const fileSize = await this.getFileSize(file);
    if (!this.uploadService.validateFileSize(fileSize)) {
      throw new Error('文件大小超过限制（50MB）');
    }

    // 直接在这里处理文件上传和照片记录创建
    const result = await this.uploadAndCreatePhoto(file, {
      photoName: photoName || file.filename,
      entityType,
      entityId,
    });

    return {
      url: result.url,
      photoId: result.photoId,
      filename: file.filename,
      size: fileSize,
    };
  }

  /**
   * 删除照片文件
   */
  @Del('/:id/file')
  async deletePhotoFile(@Param('id') id: number) {
    await this.photoService.deletePhoto(id);
    return { message: '照片删除成功' };
  }

  /**
   * 上传文件并创建照片记录
   */
  private async uploadAndCreatePhoto(
    file: UploadFileInfo,
    options: {
      photoName: string;
      entityType?: 'mountain' | 'waterSystem' | 'historicalElement';
      entityId?: number;
    }
  ): Promise<{ url: string; photoId: number }> {
    // 生成唯一文件名
    const fileExtension = this.getFileExtension(file.filename);
    const fileName = `${this.generateUniqueId()}${fileExtension}`;

    // 按日期创建目录
    const today = new Date();
    const dateDir = `${today.getFullYear()}/${String(
      today.getMonth() + 1
    ).padStart(2, '0')}/${String(today.getDate()).padStart(2, '0')}`;

    // 使用配置的上传路径
    const uploadConfig = this.uploadService.uploadConfig;
    const baseUploadDir = uploadConfig.useProjectPath
      ? join(process.cwd(), 'public', 'uploads')
      : uploadConfig.uploadPath;

    // 确保基础上传目录存在
    await this.ensureDirectoryExists(baseUploadDir);

    const uploadDir = join(baseUploadDir, dateDir);

    // 确保完整目录路径存在
    await this.ensureDirectoryExists(uploadDir);

    // 文件保存路径
    const filePath = join(uploadDir, fileName);
    const url = `${uploadConfig.baseUrl}/${dateDir}/${fileName}`;

    // 保存文件
    await this.saveFile(file, filePath);

    // 创建照片记录
    const photoDto: CreatePhotoDTO = {
      name: options.photoName,
      url,
      path: filePath,
    };

    // 设置关联实体
    if (options.entityType && options.entityId) {
      switch (options.entityType) {
        case 'mountain':
          photoDto.mountainId = options.entityId;
          break;
        case 'waterSystem':
          photoDto.waterSystemId = options.entityId;
          break;
        case 'historicalElement':
          photoDto.historicalElementId = options.entityId;
          break;
      }
    }

    const photo = await this.photoService.createPhoto(photoDto);

    return { url, photoId: photo.id };
  }

  /**
   * 获取文件大小
   */
  private async getFileSize(file: UploadFileInfo): Promise<number> {
    if (typeof file.data === 'string') {
      // 在busboy的file模式下，data是临时文件路径
      try {
        const stats = await fs.stat(file.data);
        return stats.size;
      } catch {
        return 0;
      }
    } else {
      return 0;
    }
  }

  /**
   * 获取文件扩展名
   */
  private getFileExtension(filename: string): string {
    const lastDotIndex = filename.lastIndexOf('.');
    return lastDotIndex !== -1 ? filename.substring(lastDotIndex) : '';
  }

  /**
   * 生成唯一ID
   */
  private generateUniqueId(): string {
    return uuidv4();
  }

  /**
   * 确保目录存在
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await fs.access(dirPath);
    } catch {
      await fs.mkdir(dirPath, { recursive: true });
    }
  }

  /**
   * 保存文件
   */
  private async saveFile(
    file: UploadFileInfo,
    filePath: string
  ): Promise<void> {
    // 在busboy的file模式下，file.data是临时文件路径
    if (typeof file.data === 'string') {
      // 复制临时文件到目标位置
      await fs.copyFile(file.data, filePath);
    } else {
      throw new Error('不支持的文件数据类型');
    }
  }
}
