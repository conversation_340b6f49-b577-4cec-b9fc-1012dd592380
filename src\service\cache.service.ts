import { Provide, Inject, Scope, ScopeEnum } from '@midwayjs/core';
import { RedisService } from '@midwayjs/redis';
import { RegionDict } from '../entity/region-dict.entity';
import { TypeDict } from '../entity/type-dict.entity';
import { RelationshipDict } from '../entity/relationship-dict.entity';

@Scope(ScopeEnum.Request, { allowDowngrade: true })
@Provide()
export class CacheService {
  @Inject()
  redisService: RedisService;

  // 缓存键前缀
  private readonly CACHE_PREFIX = 'zhyj:dict:';
  private readonly CACHE_TTL = 24 * 60 * 60; // 24小时

  /**
   * 加载所有字典数据到缓存
   */
  async loadDictionaryCache() {
    console.log('📦 开始加载字典缓存...');

    // 加载区域字典
    await this.loadRegionDictCache();

    // 加载类型字典
    await this.loadTypeDictCache();

    // 加载关系字典
    await this.loadRelationshipDictCache();

    console.log('📦 字典缓存加载完成');
  }

  /**
   * 加载区域字典缓存
   */
  private async loadRegionDictCache() {
    const regions = await RegionDict.findAll({
      // where: { status: 1 } as any,
      order: [
        ['sort', 'ASC'],
        ['id', 'ASC'],
      ],
    });

    const cacheKey = `${this.CACHE_PREFIX}region`;
    await this.redisService.setex(
      cacheKey,
      this.CACHE_TTL,
      JSON.stringify(regions)
    );
    console.log(`  ✓ 区域字典缓存已加载 (${regions.length} 条记录)`);
  }

  /**
   * 加载类型字典缓存
   */
  private async loadTypeDictCache() {
    const types = await TypeDict.findAll({
      // where: { status: 1 } as any,
      order: [
        ['sort', 'ASC'],
        ['id', 'ASC'],
      ],
    });

    const cacheKey = `${this.CACHE_PREFIX}type`;
    await this.redisService.setex(
      cacheKey,
      this.CACHE_TTL,
      JSON.stringify(types)
    );
    console.log(`  ✓ 类型字典缓存已加载 (${types.length} 条记录)`);
  }

  /**
   * 加载关系字典缓存
   */
  private async loadRelationshipDictCache() {
    const relationships = await RelationshipDict.findAll({
      // where: { status: 1 } as any,
      order: [
        ['sort', 'ASC'],
        ['id', 'ASC'],
      ],
    });

    const cacheKey = `${this.CACHE_PREFIX}relationship`;
    await this.redisService.setex(
      cacheKey,
      this.CACHE_TTL,
      JSON.stringify(relationships)
    );
    console.log(`  ✓ 关系字典缓存已加载 (${relationships.length} 条记录)`);
  }

  /**
   * 获取区域字典缓存
   */
  async getRegionDictCache(): Promise<any[]> {
    const cacheKey = `${this.CACHE_PREFIX}region`;
    const cached = await this.redisService.get(cacheKey);

    if (cached) {
      console.log('从缓存中获取区域字典数据');
      return JSON.parse(cached);
    }

    console.log('缓存未命中，从数据库加载区域字典数据');
    // 缓存不存在时重新加载
    await this.loadRegionDictCache();
    const newCached = await this.redisService.get(cacheKey);
    return newCached ? JSON.parse(newCached) : [];
  }

  /**
   * 获取类型字典缓存
   */
  async getTypeDictCache(): Promise<any[]> {
    const cacheKey = `${this.CACHE_PREFIX}type`;
    const cached = await this.redisService.get(cacheKey);

    if (cached) {
      return JSON.parse(cached);
    }

    // 缓存不存在时重新加载
    await this.loadTypeDictCache();
    const newCached = await this.redisService.get(cacheKey);
    return newCached ? JSON.parse(newCached) : [];
  }

  /**
   * 获取关系字典缓存
   */
  async getRelationshipDictCache(): Promise<any[]> {
    const cacheKey = `${this.CACHE_PREFIX}relationship`;
    const cached = await this.redisService.get(cacheKey);

    if (cached) {
      return JSON.parse(cached);
    }

    // 缓存不存在时重新加载
    await this.loadRelationshipDictCache();
    const newCached = await this.redisService.get(cacheKey);
    return newCached ? JSON.parse(newCached) : [];
  }

  /**
   * 清除字典缓存
   */
  async clearDictionaryCache() {
    const keys = [
      `${this.CACHE_PREFIX}region`,
      `${this.CACHE_PREFIX}type`,
      `${this.CACHE_PREFIX}relationship`,
    ];

    for (const key of keys) {
      await this.redisService.del(key);
    }

    console.log('🗑️ 字典缓存已清除');
  }

  /**
   * 刷新字典缓存
   */
  async refreshDictionaryCache() {
    await this.clearDictionaryCache();
    await this.loadDictionaryCache();
    console.log('🔄 字典缓存已刷新');
  }
}
