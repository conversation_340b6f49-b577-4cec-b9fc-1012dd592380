import { Controller, Get, Query, Inject, Param } from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { MountainService } from '../../service/mountain.service';
import { WaterSystemService } from '../../service/water-system.service';
import { HistoricalElementService } from '../../service/historical-element.service';
import { PageQueryDTO } from '../../dto/common.dto';

/**
 * 数据查询公开接口
 */
@Controller('/public/data')
export class PublicDataController {
  @Inject()
  mountainService: MountainService;

  @Inject()
  waterSystemService: WaterSystemService;

  @Inject()
  historicalElementService: HistoricalElementService;

  /**
   * 获取山塬列表
   */
  @Get('/mountain')
  @Validate()
  async getMountainList(@Query() query: PageQueryDTO) {
    const data = await this.mountainService.findMountainList(query);
    return data;
  }

  /**
   * 获取山塬详情
   */
  @Get('/mountain/:id')
  async getMountainDetail(@Query('id') id: number) {
    const data = await this.mountainService.findById(id);
    return data;
  }

  /**
   * 获取水系列表
   */
  @Get('/water-system')
  @Validate()
  async getWaterSystemList(@Query() query: PageQueryDTO) {
    const data = await this.waterSystemService.findWaterSystemList(query);
    return data;
  }

  /**
   * 获取水系详情
   */
  @Get('/water-system/:id')
  async getWaterSystemDetail(@Query('id') id: number) {
    const data = await this.waterSystemService.findById(id);
    return data;
  }

  /**
   * 获取历史要素列表
   */
  @Get('/historical-element')
  @Validate()
  async getHistoricalElementList(
    @Query() query: PageQueryDTO & { typeId?: number }
  ) {
    const data = await this.historicalElementService.findList(query);
    return data;
  }

  /**
   * 获取历史要素详情
   */
  @Get('/historical-element/:id')
  async getHistoricalElementDetail(@Query('id') id: number) {
    const data = await this.historicalElementService.findById(id);
    return data;
  }

  /**
   * 根据区域获取山塬
   */
  @Get('/mountain/by-region/:regionId')
  async getMountainsByRegion(@Query('regionId') regionId: number) {
    const data = await this.mountainService.findByRegion(regionId);
    return data;
  }

  /**
   * 根据区域获取水系
   */
  @Get('/water-system/by-region/:regionId')
  async getWaterSystemsByRegion(@Query('regionId') regionId: number) {
    const data = await this.waterSystemService.findByRegion(regionId);
    return data;
  }

  /**
   * 根据类型获取历史要素
   */
  @Get('/historical-element/by-type/:typeId')
  async getHistoricalElementsByType(@Param('typeId') typeId: number) {
    const data = await this.historicalElementService.findByType(typeId);
    return data;
  }

  /**
   * 根据区域获取历史要素
   */
  @Get('/historical-element/by-region/:regionId')
  async getHistoricalElementsByRegion(@Param('regionId') regionId: number) {
    const data = await this.historicalElementService.findByRegion(regionId);
    return data;
  }
}
