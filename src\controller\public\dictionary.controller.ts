import { Controller, Get, Inject } from '@midwayjs/core';
import { RegionDictService } from '../../service/region-dict.service';
import { TypeDictService } from '../../service/type-dict.service';
import { RelationshipDictService } from '../../service/relationship-dict.service';

/**
 * 字典数据公开接口
 */
@Controller('/public/dictionary')
export class PublicDictionaryController {
  @Inject()
  regionDictService: RegionDictService;

  @Inject()
  typeDictService: TypeDictService;

  @Inject()
  relationshipDictService: RelationshipDictService;

  /**
   * 获取区域字典列表
   */
  @Get('/region')
  async getRegionDict() {
    const data = await this.regionDictService.getRegionDictList();
    return data;
  }

  /**
   * 获取区域字典树形结构
   */
  @Get('/region/tree')
  async getRegionDictTree() {
    const data = await this.regionDictService.getRegionDictTree();
    return data;
  }

  /**
   * 获取类型字典列表
   */
  @Get('/type')
  async getTypeDict() {
    const data = await this.typeDictService.getTypeDictList();
    return data;
  }

  /**
   * 获取类型字典树形结构
   */
  @Get('/type/tree')
  async getTypeDictTree() {
    const data = await this.typeDictService.getTypeDictTree();
    return data;
  }

  /**
   * 获取关系字典列表
   */
  @Get('/relationship')
  async getRelationshipDict() {
    const data = await this.relationshipDictService.getRelationshipDictList();
    return data;
  }

  /**
   * 获取关系字典树形结构
   */
  @Get('/relationship/tree')
  async getRelationshipDictTree() {
    const data = await this.relationshipDictService.getRelationshipDictTree();
    return data;
  }
}
