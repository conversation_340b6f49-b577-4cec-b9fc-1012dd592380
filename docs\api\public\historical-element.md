# 历史要素公开接口 API 文档

## 概述

历史要素公开接口提供无需认证的数据查询功能，供前端展示和公众访问使用。包括历史要素列表查询、详情获取、按类型和区域筛选等功能。

---

## 统一响应格式

### 成功响应
所有接口成功时返回统一格式：
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 具体业务数据
  }
}
```

### 错误响应
所有接口失败时返回统一格式：
```json
{
  "errCode": 400,  // 错误码
  "msg": "错误信息"  // 错误描述
}
```

---

## 获取历史要素列表

### 接口信息

- **URL**: `/public/data/historical-element`
- **方法**: `GET`
- **认证**: 无需认证

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | number | 否 | 页码，从1开始，默认1 |
| pageSize | number | 否 | 每页数量，1-100，默认10 |
| keyword | string | 否 | 搜索关键词，按名称模糊搜索 |
| regionId | number | 否 | 区域ID筛选 |
| typeId | number | 否 | 类型ID筛选 |

### 请求示例

```bash
curl -X GET "http://localhost:7001/public/data/historical-element?page=1&pageSize=10&keyword=大雁塔&typeId=1"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "data": [
      {
        "id": 1,
        "name": "大雁塔",
        "code": "DYT001",
        "typeDictId": 1,
        "constructionLongitude": 108.9640,
        "constructionLatitude": 34.2180,
        "locationDescription": "位于西安市雁塔区大慈恩寺内",
        "constructionTime": "652-01-01T00:00:00.000Z",
        "historicalRecords": "大雁塔又名"慈恩寺塔"，唐永徽三年（652年），玄奘为保存由天竺经丝绸之路带回长安的经卷佛像主持修建了大雁塔。",
        "regionDictId": 1,
        "typeDict": {
          "id": 1,
          "typeName": "佛塔",
          "typeCode": "PAGODA"
        },
        "regionDict": {
          "id": 1,
          "regionName": "关中地区",
          "regionCode": "GUANZHONG"
        }
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  }
}
```

---

## 获取历史要素详情

### 接口信息

- **URL**: `/public/data/historical-element/{id}`
- **方法**: `GET`
- **认证**: 无需认证

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 历史要素ID |

### 请求示例

```bash
curl -X GET "http://localhost:7001/public/data/historical-element/1"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "name": "大雁塔",
    "code": "DYT001",
    "typeDictId": 1,
    "constructionLongitude": 108.9640,
    "constructionLatitude": 34.2180,
    "locationDescription": "位于西安市雁塔区大慈恩寺内",
    "constructionTime": "652-01-01T00:00:00.000Z",
    "historicalRecords": "大雁塔又名"慈恩寺塔"，唐永徽三年（652年），玄奘为保存由天竺经丝绸之路带回长安的经卷佛像主持修建了大雁塔。",
    "regionDictId": 1,
    "typeDict": {
      "id": 1,
      "typeName": "佛塔",
      "typeCode": "PAGODA"
    },
    "regionDict": {
      "id": 1,
      "regionName": "关中地区",
      "regionCode": "GUANZHONG"
    },
    "photos": [
      {
        "id": 1,
        "name": "大雁塔全景",
        "url": "/public/uploads/2024/01/15/dayanta_1642234567890.jpg"
      }
    ]
  }
}
```

---

## 根据类型获取历史要素

### 接口信息

- **URL**: `/public/data/historical-element/by-type/{typeId}`
- **方法**: `GET`
- **认证**: 无需认证

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| typeId | number | 是 | 类型ID |

### 请求示例

```bash
curl -X GET "http://localhost:7001/public/data/historical-element/by-type/1"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "id": 1,
      "name": "大雁塔",
      "code": "DYT001",
      "constructionLongitude": 108.9640,
      "constructionLatitude": 34.2180,
      "constructionTime": "652-01-01T00:00:00.000Z",
      "regionDictId": 1
    },
    {
      "id": 2,
      "name": "小雁塔",
      "code": "XYT001",
      "constructionLongitude": 108.9395,
      "constructionLatitude": 34.2370,
      "constructionTime": "707-01-01T00:00:00.000Z",
      "regionDictId": 1
    }
  ]
}
```

---

## 根据区域获取历史要素

### 接口信息

- **URL**: `/public/data/historical-element/by-region/{regionId}`
- **方法**: `GET`
- **认证**: 无需认证

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 是 | 区域ID |

### 请求示例

```bash
curl -X GET "http://localhost:7001/public/data/historical-element/by-region/1"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "id": 1,
      "name": "大雁塔",
      "code": "DYT001",
      "typeDictId": 1,
      "constructionLongitude": 108.9640,
      "constructionLatitude": 34.2180,
      "constructionTime": "652-01-01T00:00:00.000Z"
    },
    {
      "id": 3,
      "name": "华清池",
      "code": "HQC001",
      "typeDictId": 2,
      "constructionLongitude": 109.2120,
      "constructionLatitude": 34.3620,
      "constructionTime": "747-01-01T00:00:00.000Z"
    }
  ]
}
```

---

## 获取时间轴数据

### 接口信息

- **URL**: `/public/statistic/timeline`
- **方法**: `GET`
- **认证**: 无需认证

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 否 | 区域ID，筛选特定区域的时间轴数据 |

### 请求示例

```bash
curl -X GET "http://localhost:7001/public/statistic/timeline?regionId=1"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "year": 652,
      "count": 1,
      "elements": [
        {
          "id": 1,
          "name": "大雁塔",
          "type": "historical_element"
        }
      ]
    },
    {
      "year": 707,
      "count": 1,
      "elements": [
        {
          "id": 2,
          "name": "小雁塔",
          "type": "historical_element"
        }
      ]
    }
  ]
}
```

---

## 数据字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | number | 历史要素唯一标识 |
| name | string | 历史要素名称 |
| code | string | 历史要素编号 |
| typeDictId | number | 所属类型ID |
| constructionLongitude | number | 建筑经度坐标（WGS84） |
| constructionLatitude | number | 建筑纬度坐标（WGS84） |
| locationDescription | string | 位置描述 |
| constructionTime | string | 建造时间 |
| historicalRecords | string | 历史文献记载 |
| regionDictId | number | 所属区域ID |
| typeDict | object | 类型详细信息 |
| regionDict | object | 区域详细信息 |
| photos | array | 关联的照片列表 |
| createdAt | string | 创建时间 |
| updatedAt | string | 更新时间 |

---

## 获取基础统计数据

### 接口信息

- **URL**: `/public/statistic/basic`
- **方法**: `GET`
- **认证**: 无需认证

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 否 | 区域ID |
| startTime | string | 否 | 开始时间，ISO日期格式 |
| endTime | string | 否 | 结束时间，ISO日期格式 |

### 请求示例

```bash
curl -X GET "http://localhost:7001/public/statistic/basic?regionId=1"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "counts": {
      "mountain": 25,
      "waterSystem": 15,
      "historicalElement": 50
    },
    "regionStats": [
      {
        "region": "关中地区",
        "regionId": 1,
        "mountainCount": 15,
        "waterSystemCount": 10,
        "historicalElementCount": 35,
        "total": 60
      }
    ],
    "timelineData": [
      {
        "year": 652,
        "count": 1,
        "elements": [
          {
            "id": 1,
            "name": "大雁塔",
            "type": "historical_element"
          }
        ]
      }
    ]
  }
}
```

---

## 获取数据概览

### 接口信息

- **URL**: `/public/statistic/overview`
- **方法**: `GET`
- **认证**: 无需认证

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 否 | 区域ID |

### 请求示例

```bash
curl -X GET "http://localhost:7001/public/statistic/overview?regionId=1"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "totalCounts": {
      "mountain": 25,
      "waterSystem": 15,
      "historicalElement": 50
    },
    "regionStats": [
      {
        "region": "关中地区",
        "regionId": 1,
        "mountainCount": 15,
        "waterSystemCount": 10,
        "historicalElementCount": 35,
        "total": 60
      }
    ],
    "timelineData": [
      {
        "year": 652,
        "count": 1,
        "elements": [
          {
            "id": 1,
            "name": "大雁塔",
            "type": "historical_element"
          }
        ]
      }
    ]
  }
}
```

---

## 常见错误码

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 业务错误 |
| 404 | 资源不存在 |
| 422 | 参数校验错误 |
| 500 | 服务器内部错误 |

---

## 注意事项

1. **无需认证**: 所有公开接口都无需认证，可直接访问
2. **数据只读**: 公开接口只提供数据查询，不支持数据修改
3. **分页限制**: 每页最多返回100条数据
4. **坐标系统**: 使用WGS84坐标系统
5. **时间格式**: 建造时间使用ISO 8601格式
6. **缓存优化**: 数据会进行缓存优化，提高查询性能
7. **关联数据**: 详情接口会返回关联的类型、区域和照片信息
8. **搜索功能**: 支持按名称模糊搜索和多条件筛选
9. **统计数据**: 统计接口提供实时计算的数据概览
10. **时间轴**: 时间轴数据按年份分组，展示历史发展脉络
