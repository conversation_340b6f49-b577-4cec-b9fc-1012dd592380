# 字典管理模块说明

## 概述

字典管理模块已按照业务类型进行了拆分，每个字典类型都有独立的服务和控制器，便于维护和扩展。

## 模块结构

### 1. 文件组织

```
src/
├── dto/
│   └── dictionary.dto.ts          # 字典相关DTO定义
├── service/
│   ├── region-dict.service.ts     # 区域字典服务
│   ├── type-dict.service.ts       # 类型字典服务
│   └── relationship-dict.service.ts # 关系字典服务
├── controller/admin/
│   ├── region-dict.controller.ts  # 区域字典控制器
│   ├── type-dict.controller.ts    # 类型字典控制器
│   └── relationship-dict.controller.ts # 关系字典控制器
├── utils/
│   └── tree.util.ts               # 树形结构工具类
└── entity/
    ├── region-dict.entity.ts      # 区域字典实体
    ├── type-dict.entity.ts        # 类型字典实体
    └── relationship-dict.entity.ts # 关系字典实体
```

### 2. API 路由

- **区域字典**: `/admin/region-dict`
- **类型字典**: `/admin/type-dict`
- **关系字典**: `/admin/relationship-dict`

## 功能特性

### 1. 通用功能

每个字典类型都支持以下功能：

- ✅ 创建字典项
- ✅ 更新字典项
- ✅ 删除字典项（级联删除子级）
- ✅ 获取字典项详情
- ✅ 分页查询（支持搜索和筛选）
- ✅ 获取树形结构（智能清理空children）
- ✅ 获取所有字典项（缓存）
- ✅ 批量更新状态
- ✅ 启用/禁用字典项

### 2. 树形结构工具

新增了通用的树形结构工具类 `src/utils/tree.util.ts`，提供以下功能：

- **buildTree**: 构建树形结构，自动清理空children属性
- **flattenTree**: 扁平化树形结构
- **findNodeInTree**: 在树中查找节点
- **getNodePath**: 获取节点的父级路径
- **getNodeChildren**: 获取节点的所有子级（扁平化）

**特性**:
- 🔧 **代码复用**: 三个字典服务共用同一套树形结构逻辑
- 🧹 **智能清理**: 自动删除空的children属性，减少数据传输
- 🎯 **类型安全**: 支持TypeScript泛型，保证类型安全
- ⚡ **高性能**: 使用Map优化查找性能

### 3. 特殊功能

#### 区域字典
- 支持无限层级的地理区域管理
- 与山峰、水系、历史要素关联
- 提供树形结构展示

#### 类型字典
- 支持历史要素类型分类
- 支持类型层级管理
- 与历史要素关联

#### 关系字典
- 定义实体间的关系类型
- 支持关系层级管理
- 用于关系数据管理
- **新增**: 支持树形结构展示

## 数据模型

### 1. 通用字段

所有字典表都包含以下通用字段：

```typescript
{
  id: number;           // 主键ID
  status: number;       // 状态（0禁用，1启用）
  sort: number;         // 排序号
  parentId: number;     // 父级ID（支持树形结构）
  createdAt: Date;      // 创建时间
  updatedAt: Date;      // 更新时间
}
```

### 2. 特有字段

#### 区域字典
```typescript
{
  regionCode: string;   // 区域编码（唯一）
  regionName: string;   // 区域名称
  regionDesc: string;   // 区域描述
}
```

#### 类型字典
```typescript
{
  typeCode: string;     // 类型编码（唯一）
  typeName: string;     // 类型名称
  typeDesc: string;     // 类型描述
}
```

#### 关系字典
```typescript
{
  relationCode: string; // 关系编码（唯一）
  relationName: string; // 关系名称
  relationDesc: string; // 关系描述
}
```

## 使用示例

### 1. 创建区域字典

```typescript
// POST /admin/region-dict/
{
  "regionCode": "BJ",
  "regionName": "北京市",
  "parentId": null,
  "status": 1,
  "sort": 1,
  "regionDesc": "首都"
}
```

### 2. 分页查询

```typescript
// GET /admin/region-dict/?page=1&pageSize=10&keyword=北京&status=1
```

### 3. 获取树形结构

```typescript
// GET /admin/region-dict/tree
```

### 4. 批量更新状态

```typescript
// PUT /admin/region-dict/batch-status
{
  "ids": [1, 2, 3],
  "status": 0
}
```

## 缓存机制

### 1. 缓存策略

- 所有字典数据都会缓存到Redis
- 缓存键格式：`dict:region`、`dict:type`、`dict:relationship`
- 任何增删改操作都会自动刷新缓存

### 2. 缓存使用

```typescript
// 获取缓存数据（推荐用于下拉框等场景）
GET /admin/region-dict/all

// 获取分页数据（推荐用于管理界面）
GET /admin/region-dict/?page=1&pageSize=10
```

## 权限控制

### 1. 认证要求

所有字典管理接口都需要：
- JWT Token 认证
- 管理员权限

### 2. 中间件

```typescript
@Controller('/admin/region-dict', { 
  middleware: [JwtMiddleware, AuthMiddleware] 
})
```

## 数据验证

### 1. 创建验证

- 编码字段：必填、唯一、最大长度50
- 名称字段：必填、最大长度255
- 父级ID：可选、必须存在

### 2. 更新验证

- 编码更新时检查唯一性
- 父级ID更新时检查存在性

### 3. 删除验证

- 自动级联删除所有子级字典
- 检查是否有关联数据（TODO）

## 错误处理

### 1. 常见错误

- `DICT_CODE_EXISTS`: 字典编码已存在
- `DICT_NOT_FOUND`: 字典不存在
- `PARENT_NOT_FOUND`: 父级字典不存在

### 2. 错误响应格式

```json
{
  "success": false,
  "code": "DICT_CODE_EXISTS",
  "message": "区域编码已存在",
  "data": null
}
```

## 测试

### 1. 单元测试

```bash
npm test -- test/service/region-dict.service.test.ts
npm test -- test/service/type-dict.service.test.ts
npm test -- test/service/relationship-dict.service.test.ts
```

### 2. 集成测试

```bash
npm test -- test/controller/admin/region-dict.controller.test.ts
```

## 性能优化

### 1. 查询优化

- 使用索引优化查询性能
- 分页查询避免全表扫描
- 树形查询使用缓存数据

### 2. 缓存优化

- 合理设置缓存过期时间
- 及时刷新缓存数据
- 避免缓存穿透

## 扩展建议

### 1. 功能扩展

- 支持字典项导入导出
- 支持字典项版本管理
- 支持字典项审核流程

### 2. 性能扩展

- 考虑使用分布式缓存
- 优化数据库查询
- 实现读写分离

## 注意事项

1. 字典编码一旦创建建议不要修改
2. 删除字典项前务必检查关联数据
3. 批量操作时注意数据一致性
4. 定期清理无效的字典项
5. 监控缓存命中率和性能指标
